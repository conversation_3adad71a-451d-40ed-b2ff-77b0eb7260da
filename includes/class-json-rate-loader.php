<?php
/**
 * JSON Rate Loader Service.
 *
 * @package WC_RoyalMail
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * JSON Rate Loader class.
 *
 * Handles loading and caching of Royal Mail rate data from local JSON files.
 * Provides methods to fetch, validate, and cache rate information for different
 * Royal Mail services and rate types.
 *
 */
class WC_RoyalMail_JSON_Rate_Loader {
	/**
	 * Local path for rate files directory relative to plugin root.
	 *
	 * @var string
	 */
	const LOCAL_RATES_PATH = 'rate-files-json/';

	/**
	 * Local rate paths configuration for different rate types.
	 * Populated dynamically from local configuration file.
	 *
	 * @var array
	 */
	private static array $local_paths = array();

	/**
	 * Current rate date being used.
	 *
	 * @var string
	 */
	private static string $current_rate_date = '';

	/**
	 * Database table name for storing rates.
	 *
	 * @var string
	 */
	private static string $table_name = 'royalmail_rates';

	/**
	 * Get plugin rates directory path.
	 *
	 * @return string Full path to the rates directory.
	 */
	private static function get_plugin_rates_path(): string {
		return plugin_dir_path( WOOCOMMERCE_SHIPPING_ROYALMAIL_FILE ) . self::LOCAL_RATES_PATH;
	}

	/**
	 * Get current rate date by scanning available directories.
	 *
	 * Scans the rate-files-json directory for date folders and returns
	 * the most recent date that is not in the future.
	 *
	 * @return string|false Current rate date (YYYY-MM-DD) or false if none found.
	 */
	private static function get_current_rate_date() {
		$rates_path = self::get_plugin_rates_path();

		if ( ! is_dir( $rates_path ) ) {
			return false;
		}

		$current_date    = current_time( 'Y-m-d' );
		$available_dates = array();

		// Scan for date directories.
		$directories = scandir( $rates_path );
		if ( false === $directories ) {
			return false;
		}

		foreach ( $directories as $dir ) {
			// Skip . and .. directories.
			if ( '.' === $dir || '..' === $dir ) {
				continue;
			}

			$dir_path = $rates_path . $dir;

			// Only consider directories.
			if ( ! is_dir( $dir_path ) ) {
				continue;
			}

			// Check if directory name is a valid date (YYYY-MM-DD format).
			if ( preg_match( '/^\d{4}-\d{2}-\d{2}$/', $dir ) ) {
				// Only include dates that are not in the future.
				if ( $dir <= $current_date ) {
					$available_dates[] = $dir;
				}
			}
		}

		if ( empty( $available_dates ) ) {
			return false;
		}

		// Sort dates and return the most recent one.
		rsort( $available_dates );

		return $available_dates[0];
	}

	/**
	 * Initialize local rate paths by scanning directories.
	 *
	 * @return bool True if paths loaded successfully, false otherwise.
	 */
	private static function init_local_paths(): bool {
		if ( ! empty( self::$local_paths ) ) {
			return true;
		}

		$current_rate_date = self::get_current_rate_date();
		if ( false === $current_rate_date ) {
			return false;
		}

		// Verify that the rate directories exist.
		$base_path = self::get_plugin_rates_path() . $current_rate_date . '/';
		if ( ! is_dir( $base_path ) ) {
			return false;
		}

		// Check that both online and regular directories exist.
		$online_path  = $base_path . 'online/';
		$regular_path = $base_path . 'regular/';

		if ( ! is_dir( $online_path ) || ! is_dir( $regular_path ) ) {
			return false;
		}

		// Set up local paths structure.
		self::$local_paths = array(
			'online'  => $online_path,
			'regular' => $regular_path,
		);
		self::$current_rate_date = $current_rate_date;

		return true;
	}

	/**
	 * Load rate data for a specific service.
	 *
	 * Attempts to load rate data from database first, then from local files if not available.
	 * Saves successful file reads to database for future use.
	 *
	 * @param string $service_slug Service slug (e.g., 'international-economy').
	 * @param string $rate_type    Rate type ('online' or 'regular').
	 *
	 * @return array|false Rate data array or false on failure.
	 *
	 */
	public static function load_rate_data( string $service_slug, string $rate_type ) {
		// Sanitize and validate input parameters.
		$service_slug = sanitize_key( $service_slug );
		$rate_type    = sanitize_key( $rate_type );

		// Create service code by combining service slug and rate type.
		$service_code = $service_slug . '_' . $rate_type;

		// Try to get rate data from database first.
		$rate_data = self::get_rate_from_database( $service_code );

		if ( false !== $rate_data ) {
			return $rate_data;
		}

		// If not in database, fetch from local file.
		$rate_data = self::fetch_from_local_file( $service_slug, $rate_type );

		if ( false !== $rate_data ) {
			// Save to database for future use.
			self::save_rate_to_database( $service_code, $rate_data );
		}

		return $rate_data;
	}

	/**
	 * Fetch rate data from local file.
	 *
	 * Reads rate data from the local JSON file for the specified service and rate type.
	 *
	 * @param string $service_slug Service slug.
	 * @param string $rate_type    Rate type.
	 *
	 * @return array|false Rate data or false on failure.
	 */
	private static function fetch_from_local_file( string $service_slug, string $rate_type ) {
		if ( ! self::init_local_paths() ) {
			return false;
		}

		// Construct the file path.
		$file_path = self::get_plugin_rates_path() .
					 self::$current_rate_date . '/' .
					 $rate_type . '/' .
					 $service_slug . '.json';

		// Check if file exists.
		if ( ! file_exists( $file_path ) ) {
			return false;
		}

		// Read file contents.
		$content = file_get_contents( $file_path );
		if ( false === $content ) {
			return false;
		}

		// Decode JSON data.
		$data = json_decode( $content, true );
		if ( ! is_array( $data ) ) {
			return false;
		}

		return self::validate_rate_data( $data );
	}

	/**
	 * Validate rate data structure.
	 *
	 * Ensures the rate data contains required fields and at least one
	 * valid rate structure (packages, zones, or compensation levels).
	 *
	 * @param array $data Rate data to validate.
	 *
	 * @return array|false Validated data or false on failure.
	 */
	private static function validate_rate_data( array $data ) {
		// Validate that we have at least one rate structure.
		$has_rate_data = false;
		if ( ! empty( $data['packages'] ) && is_array( $data['packages'] ) ) {
			$has_rate_data = true;
		} elseif ( ! empty( $data['zones'] ) && is_array( $data['zones'] ) ) {
			$has_rate_data = true;
		} elseif ( ! empty( $data['compensation'] ) && is_array( $data['compensation'] ) ) {
			$has_rate_data = true;
		}

		if ( ! $has_rate_data ) {
			return false;
		}

		return $data;
	}

	/**
	 * Get rate data from database for a specific service.
	 *
	 * @param string $service_code Service code (e.g., 'international-economy_regular').
	 *
	 * @return array|false Rate data array or false if not found.
	 */
	private static function get_rate_from_database( string $service_code ) {
		global $wpdb;

		$table_name = $wpdb->prefix . self::$table_name;

		// Get current rate data based on start_date.
		$result = $wpdb->get_row(
			$wpdb->prepare(
				"SELECT rates_json FROM {$table_name}
				WHERE service_code = %s
				AND start_date <= CURDATE()
				ORDER BY start_date DESC
				LIMIT 1",
				$service_code
			)
		);

		if ( null === $result ) {
			return false;
		}

		$rate_data = json_decode( $result->rates_json, true );

		return self::validate_rate_data( $rate_data );
	}

	/**
	 * Save rate data to database.
	 *
	 * @param string $service_code Service code (e.g., 'international-economy_regular').
	 * @param array  $rate_data    Rate data to save.
	 * @param string $start_date   Start date for the rates (defaults to current date).
	 *
	 * @return bool True on success, false on failure.
	 */
	private static function save_rate_to_database( string $service_code, array $rate_data, string $start_date = '' ): bool {
		global $wpdb;

		if ( empty( $start_date ) ) {
			$start_date = current_time( 'Y-m-d' );
		}

		$table_name = $wpdb->prefix . self::$table_name;

		// Check if this exact rate already exists.
		$existing = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT id FROM {$table_name}
				WHERE service_code = %s
				AND start_date = %s",
				$service_code,
				$start_date
			)
		);

		$rates_json = wp_json_encode( $rate_data );

		if ( $existing ) {
			// Update existing record.
			$result = $wpdb->update(
				$table_name,
				array(
					'rates_json' => $rates_json,
				),
				array(
					'id' => $existing,
				),
				array( '%s' ),
				array( '%d' )
			);
		} else {
			// Insert new record.
			$result = $wpdb->insert(
				$table_name,
				array(
					'service_code' => $service_code,
					'start_date'   => $start_date,
					'rates_json'   => $rates_json,
				),
				array( '%s', '%s', '%s' )
			);
		}

		return false !== $result;
	}

	/**
	 * Sync all available rates from local files to database.
	 *
	 * @return array Results array with success/failure counts.
	 */
	public static function sync_rates_to_database(): array {
		$results = array(
			'success' => 0,
			'failed'  => 0,
			'errors'  => array(),
		);

		// Get all available services from the Services class.
		$all_services = self::get_all_service_codes();

		if ( empty( $all_services ) ) {
			$results['errors'][] = 'No services found to sync.';
			return $results;
		}

		// Add debug information about services being processed.
		if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
			$results['debug_info'] = array(
				'total_services' => count( $all_services ),
				'services' => $all_services,
				'rate_date' => $current_rate_date,
			);
		}

		// Get current rate date from directory structure.
		$current_rate_date = self::get_current_rate_date();
		if ( false === $current_rate_date ) {
			$results['errors'][] = 'No valid rate directories found.';
			return $results;
		}

		$rate_types = array( 'regular', 'online' );

		foreach ( $rate_types as $rate_type ) {
			foreach ( $all_services as $service_slug ) {
				$service_code = $service_slug . '_' . $rate_type;

				// Fetch rate data from local file.
				$rate_data = self::fetch_from_local_file( $service_slug, $rate_type );

				if ( false !== $rate_data ) {
					$saved = self::save_rate_to_database( $service_code, $rate_data, $current_rate_date );
					if ( $saved ) {
						$results['success']++;
					} else {
						$results['failed']++;
						$results['errors'][] = "Failed to save {$service_code} to database for {$current_rate_date}";
					}
				} else {
					$results['failed']++;
					// Get more specific error message.
					$file_path = self::get_plugin_rates_path() . $current_rate_date . '/' . $rate_type . '/' . $service_slug . '.json';
					if ( ! file_exists( $file_path ) ) {
						$results['errors'][] = "File not found: {$service_slug}.json in {$rate_type} directory";
					} else {
						$results['errors'][] = "Failed to parse JSON data from {$service_slug}.json ({$rate_type})";
					}
				}
			}
		}

		update_option( 'wc_royalmail_rates_latest_sync', gmdate( 'Y-m-d' ) );
		return $results;
	}

	/**
	 * Get all service codes from the Services class.
	 *
	 * @return array Array of service codes.
	 */
	private static function get_all_service_codes(): array {
		if ( ! class_exists( 'WooCommerce\RoyalMail\Services' ) ) {
			return array();
		}

		$reflection = new ReflectionClass( 'WooCommerce\RoyalMail\Services' );
		$constants  = $reflection->getConstants();

		return array_values( $constants );
	}
}
