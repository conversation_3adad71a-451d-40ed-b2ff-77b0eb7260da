<?php
/**
 * JSON Rate Loader Service.
 *
 * @package WC_RoyalMail
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * JSON Rate Loader class.
 *
 * Handles loading and caching of Royal Mail rate data from local JSON files.
 * Provides methods to fetch, validate, and cache rate information for different
 * Royal Mail services and rate types.
 *
 */
class WC_RoyalMail_JSON_Rate_Loader {
	/**
	 * Local path for rate files directory relative to plugin root.
	 *
	 * @var string
	 */
	const LOCAL_RATES_PATH = 'rate-files-json/';

	/**
	 * Local rate paths configuration for different rate types.
	 * Populated dynamically from local configuration file.
	 *
	 * @var array
	 */
	private static array $local_paths = array();

	/**
	 * Current rate date being used.
	 *
	 * @var string
	 */
	private static string $current_rate_date = '';

	/**
	 * Database table name for storing rates.
	 *
	 * @var string
	 */
	private static string $table_name = 'royalmail_rates';

	/**
	 * Get plugin rates directory path.
	 *
	 * @return string Full path to the rates directory.
	 */
	private static function get_plugin_rates_path(): string {
		return plugin_dir_path( WOOCOMMERCE_SHIPPING_ROYALMAIL_FILE ) . self::LOCAL_RATES_PATH;
	}

	/**
	 * Initialize local rate paths from configuration.
	 *
	 * @return bool True if paths loaded successfully, false otherwise.
	 */
	private static function init_local_paths(): bool {
		if ( ! empty( self::$local_paths ) ) {
			return true;
		}

		$data = self::get_local_rate_paths_data();
		if ( false === $data ) {
			return false;
		}

		// Find current valid rates based on date.
		$current_date = current_time( 'Y-m-d' );
		$current_paths = null;
		$current_rate_date = null;

		foreach ( $data['rates-paths'] as $start_date => $paths ) {
			if ( $start_date <= $current_date ) {
				$current_paths = $paths;
				$current_rate_date = $start_date;
			}
		}

		if ( null === $current_paths || null === $current_rate_date ) {
			return false;
		}

		// Verify that the rate directories exist.
		$base_path = self::get_plugin_rates_path() . $current_rate_date . '/';
		if ( ! is_dir( $base_path ) ) {
			return false;
		}

		self::$local_paths = $current_paths;
		self::$current_rate_date = $current_rate_date;

		return true;
	}

	/**
	 * Load rate data for a specific service.
	 *
	 * Attempts to load rate data from database first, then from local files if not available.
	 * Saves successful file reads to database for future use.
	 *
	 * @param string $service_slug Service slug (e.g., 'international-economy').
	 * @param string $rate_type    Rate type ('online' or 'regular').
	 *
	 * @return array|false Rate data array or false on failure.
	 *
	 */
	public static function load_rate_data( string $service_slug, string $rate_type ) {
		// Sanitize and validate input parameters.
		$service_slug = sanitize_key( $service_slug );
		$rate_type    = sanitize_key( $rate_type );

		// Create service code by combining service slug and rate type.
		$service_code = $service_slug . '_' . $rate_type;

		// Try to get rate data from database first.
		$rate_data = self::get_rate_from_database( $service_code );

		if ( false !== $rate_data ) {
			return $rate_data;
		}

		// If not in database, fetch from local file.
		$rate_data = self::fetch_from_local_file( $service_slug, $rate_type );

		if ( false !== $rate_data ) {
			// Save to database for future use.
			self::save_rate_to_database( $service_code, $rate_data );
		}

		return $rate_data;
	}

	/**
	 * Fetch rate data from local file.
	 *
	 * Reads rate data from the local JSON file for the specified service and rate type.
	 *
	 * @param string $service_slug Service slug.
	 * @param string $rate_type    Rate type.
	 *
	 * @return array|false Rate data or false on failure.
	 */
	private static function fetch_from_local_file( string $service_slug, string $rate_type ) {
		if ( ! self::init_local_paths() ) {
			return false;
		}

		// Construct the file path.
		$file_path = self::get_plugin_rates_path() .
					 self::$current_rate_date . '/' .
					 $rate_type . '/' .
					 $service_slug . '.json';

		// Check if file exists.
		if ( ! file_exists( $file_path ) ) {
			return false;
		}

		// Read file contents.
		$content = file_get_contents( $file_path );
		if ( false === $content ) {
			return false;
		}

		// Decode JSON data.
		$data = json_decode( $content, true );
		if ( null === $data ) {
			return false;
		}

		return self::validate_rate_data( $data );
	}

	/**
	 * Validate rate data structure.
	 *
	 * Ensures the rate data contains required fields and at least one
	 * valid rate structure (packages, zones, or compensation levels).
	 *
	 * @param mixed $data Rate data to validate.
	 *
	 * @return array|false Validated data or false on failure.
	 */
	private static function validate_rate_data( $data ) {
		// Ensure data is an array.
		if ( ! is_array( $data ) ) {
			return false;
		}

		// Check required fields.
		$required_fields = array( 'service', 'year' );
		foreach ( $required_fields as $field ) {
			if ( ! isset( $data[ $field ] ) ) {
				return false;
			}
		}

		// Validate that we have at least one rate structure.
		$has_rate_data = false;
		if ( ! empty( $data['packages'] ) && is_array( $data['packages'] ) ) {
			$has_rate_data = true;
		} elseif ( ! empty( $data['zones'] ) && is_array( $data['zones'] ) ) {
			$has_rate_data = true;
		} elseif ( ! empty( $data['compensation'] ) && is_array( $data['compensation'] ) ) {
			$has_rate_data = true;
		}

		if ( ! $has_rate_data ) {
			return false;
		}

		return $data;
	}

	/**
	 * Get rate data from database for a specific service.
	 *
	 * @param string $service_code Service code (e.g., 'international-economy_regular').
	 *
	 * @return array|false Rate data array or false if not found.
	 */
	private static function get_rate_from_database( string $service_code ) {
		global $wpdb;

		$table_name = $wpdb->prefix . self::$table_name;

		// Get current rate data based on start_date.
		$result = $wpdb->get_row(
			$wpdb->prepare(
				"SELECT rates_json FROM {$table_name}
				WHERE service_code = %s
				AND start_date <= CURDATE()
				ORDER BY start_date DESC
				LIMIT 1",
				$service_code
			)
		);

		if ( null === $result ) {
			return false;
		}

		$rate_data = json_decode( $result->rates_json, true );

		return self::validate_rate_data( $rate_data );
	}

	/**
	 * Save rate data to database.
	 *
	 * @param string $service_code Service code (e.g., 'international-economy_regular').
	 * @param array  $rate_data    Rate data to save.
	 * @param string $start_date   Start date for the rates (defaults to current date).
	 *
	 * @return bool True on success, false on failure.
	 */
	private static function save_rate_to_database( string $service_code, array $rate_data, string $start_date = '' ): bool {
		global $wpdb;

		if ( empty( $start_date ) ) {
			$start_date = current_time( 'Y-m-d' );
		}

		$table_name = $wpdb->prefix . self::$table_name;

		// Check if this exact rate already exists.
		$existing = $wpdb->get_var(
			$wpdb->prepare(
				"SELECT id FROM {$table_name}
				WHERE service_code = %s
				AND start_date = %s",
				$service_code,
				$start_date
			)
		);

		$rates_json = wp_json_encode( $rate_data );

		if ( $existing ) {
			// Update existing record.
			$result = $wpdb->update(
				$table_name,
				array(
					'rates_json' => $rates_json,
				),
				array(
					'id' => $existing,
				),
				array( '%s' ),
				array( '%d' )
			);
		} else {
			// Insert new record.
			$result = $wpdb->insert(
				$table_name,
				array(
					'service_code' => $service_code,
					'start_date'   => $start_date,
					'rates_json'   => $rates_json,
				),
				array( '%s', '%s', '%s' )
			);
		}

		return false !== $result;
	}

	/**
	 * Sync all available rates from local files to database.
	 *
	 * @return array Results array with success/failure counts.
	 */
	public static function sync_rates_to_database(): array {
		$results = array(
			'success' => 0,
			'failed'  => 0,
			'errors'  => array(),
		);

		// Get all available services from the Services class.
		$all_services = self::get_all_service_codes();

		if ( empty( $all_services ) ) {
			$results['errors'][] = 'No services found to sync.';
			return $results;
		}

		// Get rate paths configuration to determine start dates.
		$rate_paths_data = self::get_local_rate_paths_data();
		if ( false === $rate_paths_data ) {
			$results['errors'][] = 'Failed to load local rate paths configuration.';
			return $results;
		}

		$rate_types = array( 'regular', 'online' );

		foreach ( $rate_paths_data['rates-paths'] as $start_date => $paths ) {
			foreach ( $rate_types as $rate_type ) {
				foreach ( $all_services as $service_slug ) {
					$service_code = $service_slug . '_' . $rate_type;

					// Fetch rate data from local file.
					$rate_data = self::fetch_from_local_file( $service_slug, $rate_type );

					if ( false !== $rate_data ) {
						$saved = self::save_rate_to_database( $service_code, $rate_data, $start_date );
						if ( $saved ) {
							$results['success']++;
						} else {
							$results['failed']++;
							$results['errors'][] = "Failed to save {$service_code} for {$start_date}";
						}
					} else {
						$results['failed']++;
						$results['errors'][] = "Failed to load {$service_code} from local file";
					}
				}
			}
		}

		update_option( 'wc_royalmail_rates_latest_sync', gmdate( 'Y-m-d' ) );
		return $results;
	}

	/**
	 * Get rate paths data from local configuration file.
	 *
	 * @return array|false Rate paths data or false on failure.
	 */
	private static function get_local_rate_paths_data() {
		$config_file = self::get_plugin_rates_path() . 'get-rates.json';

		// Check if configuration file exists.
		if ( ! file_exists( $config_file ) ) {
			return false;
		}

		// Read configuration file.
		$content = file_get_contents( $config_file );
		if ( false === $content ) {
			return false;
		}

		// Decode JSON data.
		$data = json_decode( $content, true );
		if ( null === $data ) {
			return false;
		}

		// Validate required structure.
		if ( ! isset( $data['rates-paths'] ) || ! is_array( $data['rates-paths'] ) ) {
			return false;
		}

		return $data;
	}

	/**
	 * Get all service codes from the Services class.
	 *
	 * @return array Array of service codes.
	 */
	private static function get_all_service_codes(): array {
		if ( ! class_exists( 'WooCommerce\RoyalMail\Services' ) ) {
			return array();
		}

		$reflection = new ReflectionClass( 'WooCommerce\RoyalMail\Services' );
		$constants  = $reflection->getConstants();

		return array_values( $constants );
	}

	/**
	 * Check if rates need to be synced from local files.
	 *
	 * Since we now sync on version changes, this method checks if the database
	 * is empty or if the local configuration has been updated.
	 *
	 * @return bool True if sync is needed, false otherwise.
	 */
	public static function needs_sync(): bool {
		global $wpdb;

		// Check if database has any rates.
		$table_name = $wpdb->prefix . self::$table_name;
		$rate_count = $wpdb->get_var( "SELECT COUNT(*) FROM {$table_name}" );

		// If no rates in database, sync is needed.
		if ( 0 === (int) $rate_count ) {
			return true;
		}

		// Get the latest update date from local configuration.
		$rate_paths_data = self::get_local_rate_paths_data();
		if ( false === $rate_paths_data ) {
			return false;
		}

		$latest_update_date = $rate_paths_data['latest-update'] ?? '';
		if ( empty( $latest_update_date ) ) {
			return false;
		}

		// Get the last sync date from database.
		$last_sync_date = get_option( 'wc_royalmail_rates_latest_sync', '' );

		// Sync needed if local files are newer than last sync.
		return $latest_update_date > $last_sync_date;
	}
}
