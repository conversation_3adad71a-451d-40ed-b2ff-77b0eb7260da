# QA Test Plan: Switch from Hardcoded Arrays to Local JSON File Loading

## Overview

This QA test plan covers the changes made to switch the WooCommerce Royal Mail plugin from hardcoded rate arrays to loading rates from local JSON files stored in the `rate-files-json` directory.

## Changes Summary

### Modified Files
- `includes/class-json-rate-loader.php` - Core rate loading logic updated
- `includes/class-wc-shipping-royalmail.php` - Admin interface text updates
- `includes/class-wc-shipping-royalmail-admin.php` - Admin notice text updates
- `includes/class-wc-royalmail.php` - Cron job comment updates

### Key Changes
1. **Rate Loading Source**: Changed from CDN endpoints to local JSON files
2. **Directory-Based Configuration**: Uses directory names (YYYY-MM-DD) for rate dates instead of configuration file
3. **File Path Resolution**: Uses local file system paths instead of HTTP URLs
4. **Admin Interface**: Updated button text and descriptions to reflect local file usage
5. **Error Handling**: Updated to handle file system errors instead of HTTP errors
6. **Sync Trigger**: Changed from daily cron job to version-based syncing
7. **Automatic Sync**: Rates now sync automatically when plugin version changes
8. **Simplified Architecture**: Removed dependency on get-rates.json configuration file

## Test Environment Setup

### Prerequisites
- WordPress installation with WooCommerce active
- Royal Mail plugin installed and activated
- Test products configured with various shipping requirements
- Access to WordPress admin area

### Test Data Requirements
- Products with different weights and dimensions
- Various destination countries (UK, EU, International)
- Different package types (letter, large-letter, parcel)

## Functional Tests

### Test 1: Basic Rate Loading
**Objective**: Verify that rates load correctly from local files

**Steps**:
1. Navigate to WooCommerce > Settings > Shipping > Royal Mail
2. Ensure plugin is enabled and configured
3. Add a test product to cart
4. Navigate to checkout
5. Verify shipping options are displayed

**Expected Results**:
- Shipping rates are calculated and displayed
- No errors in browser console or WordPress debug log
- Rates match expected values from JSON files

**Test Data**: Use first-class regular service with 100g letter

### Test 2: Rate Sync Functionality
**Objective**: Verify manual rate sync works with local files

**Steps**:
1. Navigate to WooCommerce > Settings > Shipping > Royal Mail
2. Scroll to "Rate Database" section
3. Note current rate count
4. Click "Sync Rates from Local Files" button
5. Wait for page reload and check admin notice

**Expected Results**:
- Success message displayed: "RoyalMail rates sync from local files completed: X successful, Y failed"
- Rate count in database increases
- No error messages displayed

### Test 3: Different Service Types
**Objective**: Verify all service types load correctly

**Test Cases**:
- UK Services: first-class, second-class, special-delivery-9am, tracked-24, tracked-48
- International: international-economy, international-standard, international-tracked
- Parcelforce: parcelforce-express-9, parcelforce-express-10, parcelforce-express-am

**Steps** (for each service):
1. Configure product to use specific service
2. Add to cart and check shipping options
3. Verify rates are calculated correctly

**Expected Results**:
- All services load without errors
- Rates match JSON file data
- Service restrictions are applied correctly

### Test 4: Rate Type Variations
**Objective**: Verify both online and regular rates work

**Steps**:
1. Test with online rates enabled
2. Test with regular rates enabled
3. Compare rate differences

**Expected Results**:
- Both rate types load successfully
- Online rates are typically lower than regular rates
- Correct rate type is used based on configuration

### Test 5: Directory-Based Configuration
**Objective**: Verify directory-based rate date detection works correctly

**Steps**:
1. Check that plugin correctly identifies current rate date from directory name
2. Verify it selects the most recent date that's not in the future
3. Test with multiple date directories (if available)
4. Verify both online and regular subdirectories are detected

**Expected Results**:
- Correct rate date identified from directory structure
- Most recent valid date selected
- Both rate types (online/regular) are accessible
- No dependency on configuration files

### Test 6: Error Handling
**Objective**: Verify graceful handling of missing files

**Steps**:
1. Temporarily rename a rate file (e.g., first-class.json)
2. Try to calculate shipping for that service
3. Check for appropriate error handling
4. Restore the file

**Expected Results**:
- No fatal errors or crashes
- Graceful fallback behavior
- Appropriate logging of missing file errors

## Integration Tests

### Test 6: Database Caching
**Objective**: Verify database caching still works with local files

**Steps**:
1. Clear rate database
2. Load a shipping rate (triggers file read and database save)
3. Load the same rate again (should use database cache)
4. Verify database contains the rate data

**Expected Results**:
- First load reads from file and saves to database
- Second load uses database cache
- Database contains correct rate data

### Test 7: Version-Based Sync Functionality
**Objective**: Verify rates sync automatically when plugin version changes

**Steps**:
1. Note current plugin version in database option `wc_royalmail_plugin_version`
2. Simulate plugin update by changing version constant
3. Trigger WordPress init action
4. Check if sync occurs automatically
5. Verify version is updated in database

**Expected Results**:
- Sync triggers automatically on version change
- Plugin version is stored correctly in database
- No sync occurs when version hasn't changed
- Old cron jobs are cleaned up on first run

### Test 8: Multi-Package Scenarios
**Objective**: Verify complex shipping calculations work

**Steps**:
1. Add multiple products with different shipping requirements
2. Test various destination countries
3. Verify package consolidation logic

**Expected Results**:
- Multiple packages handled correctly
- Shipping costs calculated accurately
- International restrictions applied properly

## Performance Tests

### Test 9: Loading Speed
**Objective**: Compare loading speed vs previous CDN method

**Steps**:
1. Measure time to load shipping rates
2. Compare with previous CDN loading times
3. Test with multiple concurrent requests

**Expected Results**:
- Local file loading should be faster than CDN
- No significant performance degradation
- Consistent response times

### Test 10: Memory Usage
**Objective**: Verify memory usage is acceptable

**Steps**:
1. Monitor memory usage during rate loading
2. Test with multiple services loaded simultaneously
3. Check for memory leaks

**Expected Results**:
- Memory usage within acceptable limits
- No memory leaks detected
- Efficient file reading and JSON parsing

## Security Tests

### Test 11: File Access Security
**Objective**: Verify local files are accessed securely

**Steps**:
1. Verify files are read with proper permissions
2. Check that file paths are properly sanitized
3. Test with malformed file paths

**Expected Results**:
- Files accessed with appropriate permissions
- No directory traversal vulnerabilities
- Proper input sanitization

### Test 12: Data Validation
**Objective**: Verify JSON data is properly validated

**Steps**:
1. Test with malformed JSON files
2. Test with missing required fields
3. Verify data sanitization

**Expected Results**:
- Malformed JSON handled gracefully
- Missing fields don't cause errors
- All data properly sanitized

## Regression Tests

### Test 13: Existing Functionality
**Objective**: Verify no existing functionality is broken

**Test Areas**:
- Product shipping settings
- Checkout process
- Order processing
- Admin settings
- Rate calculations

**Expected Results**:
- All existing functionality works as before
- No new bugs introduced
- User experience unchanged

### Test 14: Plugin Compatibility
**Objective**: Verify compatibility with other plugins

**Steps**:
1. Test with common WooCommerce plugins
2. Test with caching plugins
3. Test with other shipping plugins

**Expected Results**:
- No conflicts with other plugins
- Caching works correctly
- Shipping calculations remain accurate

## Edge Cases

### Test 15: File System Issues
**Objective**: Test behavior when file system has issues

**Test Scenarios**:
- Insufficient file permissions
- Disk space issues
- Corrupted JSON files
- Missing rate directories

**Expected Results**:
- Graceful error handling
- Appropriate fallback behavior
- Clear error messages for administrators

### Test 16: Large Scale Testing
**Objective**: Test with large numbers of products and orders

**Steps**:
1. Test with 100+ products in cart
2. Test with high concurrent user load
3. Monitor system performance

**Expected Results**:
- System handles large loads gracefully
- Performance remains acceptable
- No timeout or memory issues

## Acceptance Criteria

### Must Pass
- [ ] All shipping rates load correctly from local files
- [ ] Manual sync functionality works without errors
- [ ] No existing functionality is broken
- [ ] Performance is equal to or better than CDN loading
- [ ] All error conditions are handled gracefully

### Should Pass
- [ ] Admin interface clearly indicates local file usage
- [ ] Cron job sync works correctly
- [ ] Database caching provides performance benefits
- [ ] Memory usage is optimized

### Nice to Have
- [ ] Loading speed is noticeably faster than CDN
- [ ] Error messages are user-friendly
- [ ] Debug logging provides useful information

## Test Execution Checklist

- [ ] Test environment prepared
- [ ] All functional tests completed
- [ ] Integration tests passed
- [ ] Performance tests show acceptable results
- [ ] Security tests reveal no vulnerabilities
- [ ] Regression tests confirm no broken functionality
- [ ] Edge cases handled appropriately
- [ ] All acceptance criteria met

## Sign-off

**Tester**: _________________ **Date**: _________

**Developer**: _________________ **Date**: _________

**Product Owner**: _________________ **Date**: _________

---

## Additional Notes

### Known Limitations
- Plugin package size increased due to bundled JSON files
- Rate updates require plugin updates instead of automatic CDN sync
- Local files must be kept in sync with Royal Mail rate changes
- Version-based syncing means rates only update with plugin updates
- Rate date determined by directory name - must follow YYYY-MM-DD format

### Future Considerations
- Consider implementing rate file update mechanism
- Monitor file system performance under high load
- Plan for rate file maintenance and updates

### Reference Documentation
- [WooCommerce Coding Standards](https://developer.woocommerce.org/docs/best-practices/coding-standards/)
- [WordPress Plugin Development Best Practices](https://developer.wordpress.org/plugins/plugin-basics/best-practices/)
- [Royal Mail API Documentation](https://developer.royalmail.net/)
