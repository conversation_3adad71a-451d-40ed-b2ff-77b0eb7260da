[![CI](https://github.com/woocommerce/woocommerce-shipping-royalmail/actions/workflows/merge_to_trunk.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-royalmail/actions/workflows/merge_to_trunk.yml)
[![CI](https://github.com/woocommerce/woocommerce-shipping-royalmail/actions/workflows/cron_qit.yml/badge.svg)](https://github.com/woocommerce/woocommerce-shipping-royalmail/actions/workflows/cron_qit.yml)

woocommerce-shipping-royalmail
==============================

Shipping method that allows you calculate shipping using Royal Mail rates. This plugin contains the Royal Mail prices and services, as they do not provide an API.

Royal Mail can calculate rates worldwide or UK domestic.

Since version 2.5.3 this plugin supports international Parcelforce rates (globaleconomy, globalvalue, globalpriority, globalexpress, irelandexpress).

| Product Page | Documentation | Ideas board | Build Status |
| ------------ | ------------- | ----------- | ------------ |
| http://woocommerce.com/products/royal-mail/ | https://docs.woocommerce.com/document/royal-mail/ | https://ideas.woocommerce.com/forums/133476-woocommerce/category/75738-category-shipping-methods | [![Build Status](https://travis-ci.com/woocommerce/woocommerce-shipping-royalmail.svg?token=pB9vx6zyNSauMrAK15Js&branch=master)](https://travis-ci.com/woocommerce/woocommerce-shipping-royalmail) |

## NPM Scripts

WooCommerce Shipping Royalmail utilizes npm scripts for task management utilities.

`pnpm run build` - Runs the tasks necessary for a release. These may include building JavaScript, SASS, CSS minification, and language files.

## Development

* Make sure tests passed. See [tests README](./tests/README.md).
* Make sure to conform coding standard rules defined in [phpcs.xml](./phpcs.xml)

## License

[GNU General Public License v3.0](http://www.gnu.org/licenses/gpl-3.0.html)
