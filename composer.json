{"name": "woocommerce/woocommerce-shipping-royalmail", "description": "A Royal Mail shipping integration for WooCommerce.", "homepage": "https://docs.woocommerce.com/document/royal-mail/", "type": "wordpress-plugin", "license": "GPL-2.0+", "repositories": [{"type": "vcs", "url": "https://github.com/woocommerce/box-packer"}], "archive": {"exclude": ["!/assets", "!/languages", "/vendor/dvdoug/boxpacker", "!/vendor/dvdoug/boxpacker/src", "!/vendor/dvdoug/boxpacker/features/bootstrap", "/vendor/woocommerce/box-packer", "!/vendor/woocommerce/box-packer/src", "node_modules", "tests", "rate-extract", "README.md", "phpcs.xml", "composer.json", "composer.lock", "package.json", "package-lock.json", "composer.json", "composer.lock", "pnpm-lock.yaml", "phpunit.xml.dist", "woocommerce-shipping-royalmail.zip", ".*"]}, "require": {"php": ">=7.4", "automattic/jetpack-autoloader": "^3", "composer/installers": "~1.2", "woocommerce/box-packer": "1.2.0", "ext-json": "*"}, "require-dev": {"phpunit/phpunit": "^10.3", "php-coveralls/php-coveralls": "^2.6", "sirbrillig/spies": "^1.10", "wp-coding-standards/wpcs": "*", "dealerdirect/phpcodesniffer-composer-installer": "*", "woocommerce/qit-cli": "*", "woocommerce/woocommerce-sniffs": "*"}, "config": {"allow-plugins": {"composer/installers": true, "dealerdirect/phpcodesniffer-composer-installer": true, "automattic/jetpack-autoloader": true}}, "scripts": {"check-security": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests --standard=./.phpcs.security.xml  --report-full --report-summary"], "check-php": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-php:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,tests --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra  --report-full --report-summary --colors"], "check-all": ["./vendor/bin/phpcs . --ignore=vendor,.git,assets,node_modules,tests --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors -s"], "check-all:fix": ["./vendor/bin/phpcbf . --ignore=vendor,.git,assets,node_modules,tests --standard=WooCommerce-Core,WordPress-Core,WordPress-Extra,WordPress-Docs  --report-full --report-summary --colors"], "qit:security": ["npm run build && composer install && ./vendor/bin/qit run:security woocommerce-shipping-royalmail --zip=woocommerce-shipping-royalmail.zip"]}}